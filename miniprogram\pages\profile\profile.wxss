/* pages/profile/profile.wxss */

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #9CA3AF;
  font-size: 28rpx;
}

/* 头部信息 */
.profile-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 60rpx 30rpx 40rpx;
  margin-bottom: 30rpx;
}

.avatar-section {
  display: flex;
  align-items: center;
}

.avatar-container {
  position: relative;
  margin-right: 40rpx;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
}

.avatar-edit-icon {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.user-basic-info {
  flex: 1;
}

.user-name {
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.user-role {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 8rpx;
}

.user-email {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #111827;
}

.edit-btn {
  display: flex;
  align-items: center;
  color: #3B82F6;
  font-size: 26rpx;
  cursor: pointer;
}

.edit-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}

.edit-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  cursor: pointer;
}

.cancel-btn {
  background: #F3F4F6;
  color: #6B7280;
}

.save-btn {
  background: #3B82F6;
  color: white;
}

/* 信息项 */
.info-item {
  display: flex;
  align-items: flex-start;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #F3F4F6;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
  margin-right: 30rpx;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #6B7280;
  line-height: 1.5;
}

.info-input {
  flex: 1;
  padding: 16rpx 20rpx;
  border: 2rpx solid #E5E7EB;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #F9FAFB;
}

.info-input:focus {
  border-color: #3B82F6;
  background: white;
}

.info-textarea {
  flex: 1;
  padding: 16rpx 20rpx;
  border: 2rpx solid #E5E7EB;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #F9FAFB;
  min-height: 120rpx;
  resize: vertical;
}

.info-textarea:focus {
  border-color: #3B82F6;
  background: white;
}

/* 菜单列表 */
.menu-list {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx;
  border-bottom: 1rpx solid #F3F4F6;
  transition: background 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: #F9FAFB;
}

.menu-icon {
  width: 60rpx;
  font-size: 36rpx;
  margin-right: 30rpx;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #374151;
}

.menu-arrow {
  font-size: 28rpx;
  color: #9CA3AF;
}

/* 退出登录 */
.logout-section {
  padding: 0 20rpx;
  margin-top: 60rpx;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .profile-header {
    padding: 40rpx 20rpx 30rpx;
  }
  
  .avatar-section {
    flex-direction: column;
    text-align: center;
  }
  
  .avatar-container {
    margin-right: 0;
    margin-bottom: 30rpx;
  }
  
  .avatar {
    width: 140rpx;
    height: 140rpx;
  }
  
  .user-name {
    font-size: 40rpx;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    padding: 20rpx 0;
  }
  
  .info-label {
    width: auto;
    margin-right: 0;
    margin-bottom: 12rpx;
  }
  
  .info-input,
  .info-textarea {
    width: 100%;
    box-sizing: border-box;
  }
  
  .menu-item {
    padding: 30rpx 20rpx;
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 600rpx) {
  .profile-header {
    padding: 30rpx 15rpx 20rpx;
  }
  
  .avatar {
    width: 120rpx;
    height: 120rpx;
  }
  
  .user-name {
    font-size: 36rpx;
  }
  
  .user-role {
    font-size: 24rpx;
  }
  
  .user-email {
    font-size: 22rpx;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 20rpx;
  }
  
  .edit-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .menu-item {
    padding: 24rpx 15rpx;
  }
  
  .menu-icon {
    width: 50rpx;
    font-size: 32rpx;
    margin-right: 20rpx;
  }
  
  .menu-text {
    font-size: 28rpx;
  }
}
