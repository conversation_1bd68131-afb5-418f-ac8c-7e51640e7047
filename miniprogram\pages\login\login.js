// pages/login/login.js
const { userApi } = require('../../utils/api.js')
const { validateEmail, showError, showSuccess } = require('../../utils/util.js')

Page({
  data: {
    email: '',
    password: '',
    loading: false,
    showPassword: false
  },

  onLoad() {
    // 检查是否已登录
    const app = getApp()
    if (app.isLoggedIn()) {
      wx.switchTab({
        url: '/pages/index/index'
      })
    }
  },

  // 输入邮箱
  onEmailInput(e) {
    this.setData({
      email: e.detail.value.trim()
    })
  },

  // 输入密码
  onPasswordInput(e) {
    this.setData({
      password: e.detail.value
    })
  },

  // 切换密码显示
  togglePasswordVisibility() {
    this.setData({
      showPassword: !this.data.showPassword
    })
  },

  // 表单验证
  validateForm() {
    const { email, password } = this.data

    if (!email) {
      showError('请输入邮箱')
      return false
    }

    if (!validateEmail(email)) {
      showError('请输入正确的邮箱格式')
      return false
    }

    if (!password) {
      showError('请输入密码')
      return false
    }

    if (password.length < 6) {
      showError('密码长度不能少于6位')
      return false
    }

    return true
  },

  // 登录
  async handleLogin() {
    if (!this.validateForm()) {
      return
    }

    const { email, password } = this.data

    try {
      this.setData({ loading: true })

      const res = await userApi.login({
        email,
        password
      })

      if (res.success) {
        const app = getApp()
        app.login(res.data.user)
        
        showSuccess('登录成功')
        
        // 跳转到首页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          })
        }, 1000)
      } else {
        showError(res.message || '登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)
      showError(error.message || '登录失败，请稍后再试')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 跳转到注册页面
  goToRegister() {
    wx.navigateTo({
      url: '/pages/register/register'
    })
  },

  // 忘记密码
  forgotPassword() {
    wx.showModal({
      title: '忘记密码',
      content: '请联系管理员重置密码',
      showCancel: false
    })
  },

  // 微信登录
  wechatLogin() {
    wx.showModal({
      title: '微信登录',
      content: '微信登录功能正在开发中',
      showCancel: false
    })
  }
})
