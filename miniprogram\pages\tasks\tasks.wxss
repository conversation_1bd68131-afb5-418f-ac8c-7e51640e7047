/* pages/tasks/tasks.wxss */

/* 搜索栏 */
.search-bar {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
  padding: 0 20rpx;
}

.search-box {
  flex: 1;
  position: relative;
}

.search-input {
  width: 100%;
  padding: 24rpx 60rpx 24rpx 24rpx;
  border: 2rpx solid #E5E7EB;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: white;
  box-sizing: border-box;
}

.search-input:focus {
  border-color: #3B82F6;
}

.search-icon {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 28rpx;
  color: #9CA3AF;
}

.filter-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  border: 2rpx solid #E5E7EB;
  border-radius: 12rpx;
  background: white;
}

.filter-icon {
  font-size: 32rpx;
  color: #6B7280;
}

/* 筛选器 */
.filters {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin: 0 20rpx 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.filter-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-label {
  width: 120rpx;
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
}

.filter-picker {
  flex: 1;
  padding: 16rpx 20rpx;
  border: 1rpx solid #E5E7EB;
  border-radius: 8rpx;
  background: #F9FAFB;
}

.picker-text {
  font-size: 26rpx;
  color: #6B7280;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #9CA3AF;
  font-size: 28rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #9CA3AF;
  margin-bottom: 40rpx;
}

/* 任务列表 */
.task-list {
  padding: 0 20rpx;
}

.task-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  position: relative;
}

.task-card:active {
  transform: scale(0.98);
}

.task-overdue {
  border-left: 8rpx solid #EF4444;
}

/* 任务头部 */
.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.task-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  color: #111827;
  margin-right: 20rpx;
  line-height: 1.4;
}

.task-actions {
  display: flex;
  gap: 12rpx;
}

/* 任务描述 */
.task-desc {
  font-size: 26rpx;
  color: #6B7280;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 任务信息 */
.task-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #6B7280;
}

.info-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}

.info-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200rpx;
}

.text-danger {
  color: #EF4444;
  font-weight: 500;
}

/* 优先级图标 */
.priority-icon {
  font-size: 32rpx;
}

.priority-low {
  color: #10B981;
}

.priority-medium {
  color: #F59E0B;
}

.priority-high {
  color: #EF4444;
}

.priority-urgent {
  color: #DC2626;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 负责人头像 */
.assignee-avatar {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

/* 逾期标识 */
.overdue-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: #EF4444;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

/* 浮动按钮 */
.fab {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  width: 112rpx;
  height: 112rpx;
  background: #3B82F6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.3);
  z-index: 100;
  transition: transform 0.3s ease;
}

.fab:active {
  transform: scale(0.9);
}

.fab-icon {
  font-size: 48rpx;
  color: white;
  font-weight: 300;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .search-bar {
    padding: 0 15rpx;
    gap: 15rpx;
  }
  
  .filters {
    margin: 0 15rpx 20rpx;
    padding: 20rpx;
  }
  
  .task-list {
    padding: 0 15rpx;
  }
  
  .task-card {
    padding: 20rpx;
  }
  
  .task-title {
    font-size: 28rpx;
  }
  
  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }
  
  .info-item {
    width: 100%;
  }
  
  .info-text {
    max-width: none;
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 600rpx) {
  .search-bar {
    flex-direction: column;
    gap: 15rpx;
  }
  
  .filter-btn {
    width: 100%;
    height: 60rpx;
  }
  
  .task-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15rpx;
  }
  
  .task-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .fab {
    bottom: 100rpx;
    right: 30rpx;
    width: 96rpx;
    height: 96rpx;
  }
  
  .fab-icon {
    font-size: 40rpx;
  }
}
