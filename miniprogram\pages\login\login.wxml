<!--pages/login/login.wxml-->
<view class="login-container">
  <!-- 头部Logo -->
  <view class="header">
    <view class="logo">
      <image src="/images/logo.png" class="logo-image" mode="aspectFit" />
    </view>
    <view class="title">LabSync</view>
    <view class="subtitle">实验室管理系统</view>
  </view>

  <!-- 登录表单 -->
  <view class="form-container">
    <view class="form-group">
      <view class="form-label">邮箱</view>
      <view class="input-wrapper">
        <input
          class="form-input"
          type="text"
          placeholder="请输入邮箱"
          value="{{email}}"
          bindinput="onEmailInput"
          maxlength="50"
        />
        <view class="input-icon">📧</view>
      </view>
    </view>

    <view class="form-group">
      <view class="form-label">密码</view>
      <view class="input-wrapper">
        <input
          class="form-input"
          type="{{showPassword ? 'text' : 'password'}}"
          placeholder="请输入密码"
          value="{{password}}"
          bindinput="onPasswordInput"
          maxlength="20"
        />
        <view class="input-icon" bindtap="togglePasswordVisibility">
          {{showPassword ? '🙈' : '👁️'}}
        </view>
      </view>
    </view>

    <!-- 登录按钮 -->
    <button
      class="btn btn-primary btn-block login-btn"
      bindtap="handleLogin"
      loading="{{loading}}"
      disabled="{{loading}}"
    >
      {{loading ? '登录中...' : '登录'}}
    </button>

    <!-- 其他操作 -->
    <view class="form-actions">
      <view class="action-link" bindtap="forgotPassword">忘记密码？</view>
      <view class="action-link" bindtap="goToRegister">注册账号</view>
    </view>
  </view>

  <!-- 分割线 -->
  <view class="divider">
    <view class="divider-line"></view>
    <view class="divider-text">或</view>
    <view class="divider-line"></view>
  </view>

  <!-- 微信登录 -->
  <view class="wechat-login">
    <button
      class="btn btn-outline btn-block wechat-btn"
      bindtap="wechatLogin"
    >
      <view class="wechat-icon">💬</view>
      微信登录
    </button>
  </view>

  <!-- 底部信息 -->
  <view class="footer">
    <view class="footer-text">
      登录即表示同意
      <text class="link">用户协议</text>
      和
      <text class="link">隐私政策</text>
    </view>
  </view>
</view>
