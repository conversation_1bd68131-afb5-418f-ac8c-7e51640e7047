// pages/tasks/tasks.js
const { taskApi } = require('../../utils/api.js')
const { formatTime, getTaskStatusText, getTaskPriorityText, showError } = require('../../utils/util.js')

Page({
  data: {
    tasks: [],
    loading: true,
    refreshing: false,
    searchText: '',
    filterStatus: 'ALL',
    filterPriority: 'ALL',
    statusOptions: [
      { value: 'ALL', label: '全部状态' },
      { value: 'TODO', label: '待办' },
      { value: 'IN_PROGRESS', label: '进行中' },
      { value: 'REVIEW', label: '待审核' },
      { value: 'COMPLETED', label: '已完成' }
    ],
    priorityOptions: [
      { value: 'ALL', label: '全部优先级' },
      { value: 'LOW', label: '低' },
      { value: 'MEDIUM', label: '中' },
      { value: 'HIGH', label: '高' },
      { value: 'URGENT', label: '紧急' }
    ],
    showFilters: false
  },

  onLoad() {
    this.loadTasks()
  },

  onShow() {
    // 从任务详情页返回时刷新列表
    if (this.data.tasks.length > 0) {
      this.loadTasks()
    }
  },

  // 加载任务列表
  async loadTasks() {
    try {
      this.setData({ loading: true })
      
      const res = await taskApi.getMyTasks()
      
      if (res.success) {
        this.setData({
          tasks: res.data || [],
          loading: false
        })
      } else {
        showError(res.message || '加载任务列表失败')
        this.setData({ loading: false })
      }
    } catch (error) {
      console.error('加载任务列表失败:', error)
      showError('加载任务列表失败')
      this.setData({ loading: false })
    }
  },

  // 下拉刷新
  async onPullDownRefresh() {
    this.setData({ refreshing: true })
    await this.loadTasks()
    this.setData({ refreshing: false })
    wx.stopPullDownRefresh()
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchText: e.detail.value
    })
  },

  // 切换筛选器显示
  toggleFilters() {
    this.setData({
      showFilters: !this.data.showFilters
    })
  },

  // 状态筛选
  onStatusChange(e) {
    const index = e.detail.value
    this.setData({
      filterStatus: this.data.statusOptions[index].value
    })
  },

  // 优先级筛选
  onPriorityChange(e) {
    const index = e.detail.value
    this.setData({
      filterPriority: this.data.priorityOptions[index].value
    })
  },

  // 获取过滤后的任务列表
  getFilteredTasks() {
    const { tasks, searchText, filterStatus, filterPriority } = this.data
    
    return tasks.filter(task => {
      // 搜索过滤
      const matchSearch = !searchText || 
        task.title.toLowerCase().includes(searchText.toLowerCase()) ||
        (task.description && task.description.toLowerCase().includes(searchText.toLowerCase()))
      
      // 状态过滤
      const matchStatus = filterStatus === 'ALL' || task.status === filterStatus
      
      // 优先级过滤
      const matchPriority = filterPriority === 'ALL' || task.priority === filterPriority
      
      return matchSearch && matchStatus && matchPriority
    })
  },

  // 跳转到任务详情
  goToTaskDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/task-detail/task-detail?id=${id}`
    })
  },

  // 快速更新任务状态
  async updateTaskStatus(e) {
    e.stopPropagation() // 阻止事件冒泡
    
    const { id, status } = e.currentTarget.dataset
    const statusMap = {
      'TODO': 'IN_PROGRESS',
      'IN_PROGRESS': 'COMPLETED',
      'REVIEW': 'COMPLETED',
      'COMPLETED': 'TODO'
    }
    
    const newStatus = statusMap[status]
    if (!newStatus) return
    
    try {
      const res = await taskApi.updateTaskStatus(id, newStatus)
      if (res.success) {
        // 更新本地数据
        const tasks = this.data.tasks.map(task => 
          task.id === id ? { ...task, status: newStatus } : task
        )
        this.setData({ tasks })
        
        wx.showToast({
          title: '状态已更新',
          icon: 'success'
        })
      } else {
        showError(res.message || '更新失败')
      }
    } catch (error) {
      console.error('更新任务状态失败:', error)
      showError('更新失败')
    }
  },

  // 创建新任务
  createTask() {
    wx.showModal({
      title: '创建任务',
      content: '任务创建功能正在开发中',
      showCancel: false
    })
  },

  // 获取任务状态文本
  getStatusText(status) {
    return getTaskStatusText(status)
  },

  // 获取任务优先级文本
  getPriorityText(priority) {
    return getTaskPriorityText(priority)
  },

  // 获取任务状态样式类
  getStatusClass(status) {
    const classMap = {
      'TODO': 'tag-gray',
      'IN_PROGRESS': 'tag-primary',
      'REVIEW': 'tag-warning',
      'COMPLETED': 'tag-success'
    }
    return classMap[status] || 'tag-gray'
  },

  // 获取任务优先级样式类
  getPriorityClass(priority) {
    const classMap = {
      'LOW': 'priority-low',
      'MEDIUM': 'priority-medium',
      'HIGH': 'priority-high',
      'URGENT': 'priority-urgent'
    }
    return classMap[priority] || 'priority-medium'
  },

  // 格式化时间
  formatTime(time) {
    return formatTime(time, 'MM-DD')
  },

  // 检查任务是否逾期
  isOverdue(dueDate) {
    if (!dueDate) return false
    return new Date(dueDate) < new Date()
  }
})
