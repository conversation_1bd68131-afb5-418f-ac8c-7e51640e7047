<!--pages/tasks/tasks.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-box">
      <input
        class="search-input"
        type="text"
        placeholder="搜索任务..."
        value="{{searchText}}"
        bindinput="onSearchInput"
      />
      <view class="search-icon">🔍</view>
    </view>
    <view class="filter-btn" bindtap="toggleFilters">
      <text class="filter-icon">⚙️</text>
    </view>
  </view>

  <!-- 筛选器 -->
  <view wx:if="{{showFilters}}" class="filters">
    <view class="filter-row">
      <view class="filter-label">状态:</view>
      <picker
        class="filter-picker"
        mode="selector"
        range="{{statusOptions}}"
        range-key="label"
        bindchange="onStatusChange"
      >
        <view class="picker-text">
          {{statusOptions.find(item => item.value === filterStatus).label}}
        </view>
      </picker>
    </view>
    
    <view class="filter-row">
      <view class="filter-label">优先级:</view>
      <picker
        class="filter-picker"
        mode="selector"
        range="{{priorityOptions}}"
        range-key="label"
        bindchange="onPriorityChange"
      >
        <view class="picker-text">
          {{priorityOptions.find(item => item.value === filterPriority).label}}
        </view>
      </picker>
    </view>
  </view>

  <!-- 加载中 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 任务列表 -->
  <view wx:else>
    <!-- 空状态 -->
    <view wx:if="{{getFilteredTasks().length === 0}}" class="empty-state">
      <view class="empty-icon">📝</view>
      <view class="empty-text">
        {{searchText || filterStatus !== 'ALL' || filterPriority !== 'ALL' ? '没有找到匹配的任务' : '暂无任务'}}
      </view>
      <button wx:if="{{!searchText && filterStatus === 'ALL' && filterPriority === 'ALL'}}" 
              class="btn btn-primary" 
              bindtap="createTask">
        创建第一个任务
      </button>
    </view>

    <!-- 任务卡片列表 -->
    <view wx:else class="task-list">
      <view
        wx:for="{{getFilteredTasks()}}"
        wx:key="id"
        class="task-card {{isOverdue(item.dueDate) ? 'task-overdue' : ''}}"
        bindtap="goToTaskDetail"
        data-id="{{item.id}}"
      >
        <!-- 任务头部 -->
        <view class="task-header">
          <view class="task-title">{{item.title}}</view>
          <view class="task-actions">
            <view class="tag {{getStatusClass(item.status)}}" 
                  bindtap="updateTaskStatus"
                  data-id="{{item.id}}"
                  data-status="{{item.status}}">
              {{getStatusText(item.status)}}
            </view>
          </view>
        </view>

        <!-- 任务描述 -->
        <view wx:if="{{item.description}}" class="task-desc">
          {{item.description}}
        </view>

        <!-- 任务信息 -->
        <view class="task-info">
          <view class="info-row">
            <view class="info-item">
              <view class="info-icon priority-icon {{getPriorityClass(item.priority)}}">●</view>
              <view class="info-text">{{getPriorityText(item.priority)}}</view>
            </view>
            
            <view wx:if="{{item.dueDate}}" class="info-item">
              <view class="info-icon">📅</view>
              <view class="info-text {{isOverdue(item.dueDate) ? 'text-danger' : ''}}">
                {{formatTime(item.dueDate)}}
              </view>
            </view>
          </view>

          <view class="info-row">
            <view wx:if="{{item.project}}" class="info-item">
              <view class="info-icon">📊</view>
              <view class="info-text">{{item.project.title}}</view>
            </view>
            
            <view wx:if="{{item.assignee}}" class="info-item">
              <image 
                class="assignee-avatar" 
                src="{{item.assignee.avatar || '/images/default-avatar.png'}}"
                mode="aspectFill"
              />
              <view class="info-text">{{item.assignee.name}}</view>
            </view>
          </view>
        </view>

        <!-- 逾期标识 -->
        <view wx:if="{{isOverdue(item.dueDate)}}" class="overdue-badge">
          逾期
        </view>
      </view>
    </view>
  </view>

  <!-- 浮动创建按钮 -->
  <view class="fab" bindtap="createTask">
    <view class="fab-icon">+</view>
  </view>
</view>
