<!--pages/register/register.wxml-->
<view class="register-container">
  <!-- 头部 -->
  <view class="header">
    <view class="logo">
      <image src="/images/logo.png" class="logo-image" mode="aspectFit" />
    </view>
    <view class="title">加入 LabSync</view>
    <view class="subtitle">开始您的科研协作之旅</view>
  </view>

  <!-- 步骤指示器 -->
  <view class="step-indicator">
    <view class="step {{step >= 1 ? 'step-active' : ''}}">
      <view class="step-number">1</view>
      <view class="step-text">验证邀请码</view>
    </view>
    <view class="step-line {{step >= 2 ? 'line-active' : ''}}"></view>
    <view class="step {{step >= 2 ? 'step-active' : ''}}">
      <view class="step-number">2</view>
      <view class="step-text">填写信息</view>
    </view>
  </view>

  <!-- 第一步：邀请码验证 -->
  <view wx:if="{{step === 1}}" class="form-container">
    <view class="step-title">请输入邀请码</view>
    <view class="step-desc">邀请码格式：LABSYNC-YYYY-XXX</view>
    
    <view class="form-group">
      <view class="input-wrapper">
        <input
          class="form-input invite-code-input"
          type="text"
          placeholder="请输入邀请码"
          value="{{formData.inviteCode}}"
          bindinput="onInviteCodeInput"
          maxlength="16"
        />
        <view class="input-icon">🎫</view>
      </view>
    </view>

    <button
      class="btn btn-primary btn-block"
      bindtap="validateInviteCode"
      loading="{{loading}}"
      disabled="{{loading || !formData.inviteCode}}"
    >
      {{loading ? '验证中...' : '验证邀请码'}}
    </button>

    <view class="form-footer">
      <view class="footer-text">
        已有账号？
        <text class="link" bindtap="goToLogin">立即登录</text>
      </view>
    </view>
  </view>

  <!-- 第二步：填写信息 -->
  <view wx:if="{{step === 2}}" class="form-container">
    <view class="step-title">完善个人信息</view>
    <view class="step-desc">请填写您的基本信息</view>

    <!-- 返回按钮 -->
    <view class="back-btn" bindtap="goBackStep">
      <text class="back-icon">←</text>
      <text>返回上一步</text>
    </view>

    <!-- 基本信息 -->
    <view class="form-group">
      <view class="form-label">姓名 *</view>
      <view class="input-wrapper">
        <input
          class="form-input"
          type="text"
          placeholder="请输入您的姓名"
          value="{{formData.name}}"
          bindinput="onNameInput"
          maxlength="20"
        />
        <view class="input-icon">👤</view>
      </view>
    </view>

    <view class="form-group">
      <view class="form-label">邮箱 *</view>
      <view class="input-wrapper">
        <input
          class="form-input"
          type="text"
          placeholder="请输入邮箱地址"
          value="{{formData.email}}"
          bindinput="onEmailInput"
          maxlength="50"
        />
        <view class="input-icon">📧</view>
      </view>
    </view>

    <view class="form-group">
      <view class="form-label">密码 *</view>
      <view class="input-wrapper">
        <input
          class="form-input"
          type="{{showPassword ? 'text' : 'password'}}"
          placeholder="请输入密码（至少6位）"
          value="{{formData.password}}"
          bindinput="onPasswordInput"
          maxlength="20"
        />
        <view class="input-icon" bindtap="togglePasswordVisibility">
          {{showPassword ? '🙈' : '👁️'}}
        </view>
      </view>
    </view>

    <view class="form-group">
      <view class="form-label">确认密码 *</view>
      <view class="input-wrapper">
        <input
          class="form-input"
          type="{{showConfirmPassword ? 'text' : 'password'}}"
          placeholder="请再次输入密码"
          value="{{formData.confirmPassword}}"
          bindinput="onConfirmPasswordInput"
          maxlength="20"
        />
        <view class="input-icon" bindtap="toggleConfirmPasswordVisibility">
          {{showConfirmPassword ? '🙈' : '👁️'}}
        </view>
      </view>
    </view>

    <!-- 可选信息 -->
    <view class="optional-section">
      <view class="section-title">可选信息</view>
      
      <view class="form-group">
        <view class="form-label">部门</view>
        <view class="input-wrapper">
          <input
            class="form-input"
            type="text"
            placeholder="请输入所在部门"
            value="{{formData.department}}"
            bindinput="onDepartmentInput"
            maxlength="30"
          />
          <view class="input-icon">🏢</view>
        </view>
      </view>

      <view class="form-group">
        <view class="form-label">职位</view>
        <view class="input-wrapper">
          <input
            class="form-input"
            type="text"
            placeholder="请输入职位"
            value="{{formData.position}}"
            bindinput="onPositionInput"
            maxlength="30"
          />
          <view class="input-icon">💼</view>
        </view>
      </view>
    </view>

    <button
      class="btn btn-primary btn-block register-btn"
      bindtap="handleRegister"
      loading="{{loading}}"
      disabled="{{loading}}"
    >
      {{loading ? '注册中...' : '完成注册'}}
    </button>

    <view class="form-footer">
      <view class="footer-text">
        注册即表示同意
        <text class="link">用户协议</text>
        和
        <text class="link">隐私政策</text>
      </view>
    </view>
  </view>
</view>
