/* pages/chat/chat.wxss */

/* 搜索栏 */
.search-bar {
  padding: 20rpx;
  margin-bottom: 10rpx;
}

.search-box {
  position: relative;
}

.search-input {
  width: 100%;
  padding: 24rpx 60rpx 24rpx 24rpx;
  border: 2rpx solid #E5E7EB;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: white;
  box-sizing: border-box;
}

.search-input:focus {
  border-color: #3B82F6;
}

.search-icon {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 28rpx;
  color: #9CA3AF;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #9CA3AF;
  font-size: 28rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #9CA3AF;
  margin-bottom: 40rpx;
}

/* 聊天列表 */
.chat-list {
  background: white;
}

.chat-item {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #F3F4F6;
  transition: background 0.3s ease;
  position: relative;
}

.chat-item:last-child {
  border-bottom: none;
}

.chat-item:active {
  background: #F9FAFB;
}

/* 头像容器 */
.chat-avatar-container {
  position: relative;
  margin-right: 24rpx;
}

.chat-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  background: #F3F4F6;
}

.unread-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #EF4444;
  color: white;
  font-size: 20rpx;
  font-weight: 600;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 4rpx rgba(239, 68, 68, 0.3);
}

/* 在线状态指示器 */
.online-indicator {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 20rpx;
  height: 20rpx;
  background: #10B981;
  border: 3rpx solid white;
  border-radius: 50%;
}

/* 聊天信息 */
.chat-info {
  flex: 1;
  min-width: 0; /* 防止flex子项溢出 */
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.chat-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #111827;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 400rpx;
}

.chat-time {
  font-size: 22rpx;
  color: #9CA3AF;
  white-space: nowrap;
  margin-left: 20rpx;
}

.chat-preview {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.last-message {
  font-size: 26rpx;
  color: #6B7280;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 400rpx;
  line-height: 1.4;
}

.member-count {
  font-size: 22rpx;
  color: #9CA3AF;
  white-space: nowrap;
  margin-left: 20rpx;
}

/* 浮动按钮 */
.fab {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  width: 112rpx;
  height: 112rpx;
  background: #10B981;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(16, 185, 129, 0.3);
  z-index: 100;
  transition: transform 0.3s ease;
}

.fab:active {
  transform: scale(0.9);
}

.fab-icon {
  font-size: 40rpx;
  color: white;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .search-bar {
    padding: 15rpx;
  }
  
  .chat-item {
    padding: 24rpx 15rpx;
  }
  
  .chat-avatar {
    width: 80rpx;
    height: 80rpx;
  }
  
  .chat-avatar-container {
    margin-right: 20rpx;
  }
  
  .chat-name {
    font-size: 28rpx;
    max-width: 300rpx;
  }
  
  .last-message {
    font-size: 24rpx;
    max-width: 300rpx;
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 600rpx) {
  .chat-item {
    padding: 20rpx 15rpx;
  }
  
  .chat-avatar {
    width: 72rpx;
    height: 72rpx;
  }
  
  .chat-avatar-container {
    margin-right: 16rpx;
  }
  
  .chat-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4rpx;
  }
  
  .chat-time {
    margin-left: 0;
  }
  
  .chat-preview {
    flex-direction: column;
    align-items: flex-start;
    gap: 4rpx;
  }
  
  .member-count {
    margin-left: 0;
  }
  
  .chat-name,
  .last-message {
    max-width: none;
    width: 100%;
  }
  
  .fab {
    bottom: 100rpx;
    right: 30rpx;
    width: 96rpx;
    height: 96rpx;
  }
  
  .fab-icon {
    font-size: 36rpx;
  }
}

/* 特殊状态样式 */
.chat-item.has-unread .chat-name {
  font-weight: 600;
  color: #111827;
}

.chat-item.has-unread .last-message {
  color: #374151;
  font-weight: 500;
}

/* 群聊特殊样式 */
.chat-item.group-chat .chat-avatar {
  border-radius: 20%;
}

/* 系统消息样式 */
.last-message.system-message {
  color: #9CA3AF;
  font-style: italic;
}
