<!--components/loading/loading.wxml-->
<view wx:if="{{show}}" class="loading-container {{mask ? 'loading-mask' : ''}}">
  <view class="loading-content">
    <!-- Spinner 加载动画 -->
    <view wx:if="{{type === 'spinner'}}" class="loading-spinner loading-{{size}}">
      <view class="spinner-circle"></view>
    </view>
    
    <!-- Dots 加载动画 -->
    <view wx:elif="{{type === 'dots'}}" class="loading-dots loading-{{size}}">
      <view class="dot dot1"></view>
      <view class="dot dot2"></view>
      <view class="dot dot3"></view>
    </view>
    
    <!-- Pulse 加载动画 -->
    <view wx:elif="{{type === 'pulse'}}" class="loading-pulse loading-{{size}}">
      <view class="pulse-circle"></view>
    </view>
    
    <!-- 加载文本 -->
    <view wx:if="{{text}}" class="loading-text">{{text}}</view>
  </view>
  
  <!-- 遮罩层 -->
  <view wx:if="{{mask}}" class="loading-backdrop" bindtap="onMaskTap"></view>
</view>
