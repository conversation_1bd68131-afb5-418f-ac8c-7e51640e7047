/* components/loading/loading.wxss */

.loading-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.3);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.loading-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

/* Spinner 动画 */
.loading-spinner {
  position: relative;
}

.spinner-circle {
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3B82F6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dots 动画 */
.loading-dots {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.dot {
  border-radius: 50%;
  background: #3B82F6;
  animation: bounce 1.4s ease-in-out infinite both;
}

.dot1 {
  animation-delay: -0.32s;
}

.dot2 {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Pulse 动画 */
.loading-pulse {
  position: relative;
}

.pulse-circle {
  border-radius: 50%;
  background: #3B82F6;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* 尺寸变体 */
.loading-small .spinner-circle {
  width: 40rpx;
  height: 40rpx;
}

.loading-small .dot {
  width: 12rpx;
  height: 12rpx;
}

.loading-small .pulse-circle {
  width: 40rpx;
  height: 40rpx;
}

.loading-medium .spinner-circle {
  width: 60rpx;
  height: 60rpx;
}

.loading-medium .dot {
  width: 16rpx;
  height: 16rpx;
}

.loading-medium .pulse-circle {
  width: 60rpx;
  height: 60rpx;
}

.loading-large .spinner-circle {
  width: 80rpx;
  height: 80rpx;
}

.loading-large .dot {
  width: 20rpx;
  height: 20rpx;
}

.loading-large .pulse-circle {
  width: 80rpx;
  height: 80rpx;
}

/* 加载文本 */
.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
  text-align: center;
}
