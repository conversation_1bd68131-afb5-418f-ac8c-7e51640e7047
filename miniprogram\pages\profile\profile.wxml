<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 加载中 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 个人资料内容 -->
  <view wx:else>
    <!-- 头部信息卡片 -->
    <view class="profile-header">
      <view class="avatar-section">
        <view class="avatar-container" bindtap="chooseAvatar">
          <image 
            class="avatar" 
            src="{{userInfo.avatar || '/images/default-avatar.png'}}"
            mode="aspectFill"
          />
          <view class="avatar-edit-icon">📷</view>
        </view>
        
        <view class="user-basic-info">
          <view class="user-name">{{userInfo.name}}</view>
          <view class="user-role">{{getRoleText(userInfo.role)}}</view>
          <view class="user-email">{{userInfo.email}}</view>
        </view>
      </view>
    </view>

    <!-- 详细信息卡片 -->
    <view class="card">
      <view class="card-header">
        <view class="card-title">个人信息</view>
        <view wx:if="{{!editing}}" class="edit-btn" bindtap="startEdit">
          <text class="edit-icon">✏️</text>
          <text>编辑</text>
        </view>
        <view wx:else class="edit-actions">
          <view class="action-btn cancel-btn" bindtap="cancelEdit">取消</view>
          <view class="action-btn save-btn" bindtap="saveEdit">保存</view>
        </view>
      </view>
      
      <view class="card-body">
        <!-- 姓名 -->
        <view class="info-item">
          <view class="info-label">姓名</view>
          <view wx:if="{{!editing}}" class="info-value">
            {{userInfo.name || '未设置'}}
          </view>
          <input wx:else 
                 class="info-input" 
                 value="{{tempUserInfo.name}}" 
                 bindinput="onNameInput"
                 placeholder="请输入姓名" />
        </view>

        <!-- 个人简介 -->
        <view class="info-item">
          <view class="info-label">个人简介</view>
          <view wx:if="{{!editing}}" class="info-value">
            {{userInfo.bio || '未设置'}}
          </view>
          <textarea wx:else 
                    class="info-textarea" 
                    value="{{tempUserInfo.bio}}" 
                    bindinput="onBioInput"
                    placeholder="请输入个人简介"
                    maxlength="200" />
        </view>

        <!-- 手机号 -->
        <view class="info-item">
          <view class="info-label">手机号</view>
          <view wx:if="{{!editing}}" class="info-value">
            {{userInfo.phone || '未设置'}}
          </view>
          <input wx:else 
                 class="info-input" 
                 type="number"
                 value="{{tempUserInfo.phone}}" 
                 bindinput="onPhoneInput"
                 placeholder="请输入手机号" />
        </view>

        <!-- 部门 -->
        <view class="info-item">
          <view class="info-label">部门</view>
          <view wx:if="{{!editing}}" class="info-value">
            {{userInfo.department || '未设置'}}
          </view>
          <input wx:else 
                 class="info-input" 
                 value="{{tempUserInfo.department}}" 
                 bindinput="onDepartmentInput"
                 placeholder="请输入部门" />
        </view>

        <!-- 职位 -->
        <view class="info-item">
          <view class="info-label">职位</view>
          <view wx:if="{{!editing}}" class="info-value">
            {{userInfo.position || '未设置'}}
          </view>
          <input wx:else 
                 class="info-input" 
                 value="{{tempUserInfo.position}}" 
                 bindinput="onPositionInput"
                 placeholder="请输入职位" />
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-list">
      <view class="menu-item" bindtap="viewStats">
        <view class="menu-icon">📊</view>
        <view class="menu-text">个人统计</view>
        <view class="menu-arrow">></view>
      </view>
      
      <view class="menu-item" bindtap="openSettings">
        <view class="menu-icon">⚙️</view>
        <view class="menu-text">设置</view>
        <view class="menu-arrow">></view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-section">
      <button class="btn btn-danger btn-block" bindtap="logout">
        退出登录
      </button>
    </view>
  </view>
</view>
