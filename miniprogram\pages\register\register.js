// pages/register/register.js
const { userApi } = require('../../utils/api.js')
const { validateEmail, showError, showSuccess } = require('../../utils/util.js')

Page({
  data: {
    formData: {
      inviteCode: '',
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
      department: '',
      position: ''
    },
    loading: false,
    showPassword: false,
    showConfirmPassword: false,
    step: 1, // 1: 邀请码验证, 2: 填写信息
    inviteCodeValid: false
  },

  onLoad(options) {
    // 如果从URL参数传入邀请码
    if (options.inviteCode) {
      this.setData({
        'formData.inviteCode': options.inviteCode
      })
    }
  },

  // 邀请码输入
  onInviteCodeInput(e) {
    this.setData({
      'formData.inviteCode': e.detail.value.trim().toUpperCase()
    })
  },

  // 验证邀请码
  async validateInviteCode() {
    const { inviteCode } = this.data.formData

    if (!inviteCode) {
      showError('请输入邀请码')
      return
    }

    // 简单的邀请码格式验证
    if (!/^LABSYNC-\d{4}-\d{3}$/.test(inviteCode)) {
      showError('邀请码格式不正确')
      return
    }

    try {
      this.setData({ loading: true })

      // 这里应该调用API验证邀请码
      // const res = await userApi.validateInviteCode(inviteCode)
      
      // 模拟验证成功
      setTimeout(() => {
        this.setData({
          loading: false,
          inviteCodeValid: true,
          step: 2
        })
        showSuccess('邀请码验证成功')
      }, 1000)

    } catch (error) {
      console.error('验证邀请码失败:', error)
      showError('验证失败，请稍后再试')
      this.setData({ loading: false })
    }
  },

  // 返回上一步
  goBackStep() {
    this.setData({
      step: 1,
      inviteCodeValid: false
    })
  },

  // 表单输入处理
  onNameInput(e) {
    this.setData({
      'formData.name': e.detail.value.trim()
    })
  },

  onEmailInput(e) {
    this.setData({
      'formData.email': e.detail.value.trim()
    })
  },

  onPasswordInput(e) {
    this.setData({
      'formData.password': e.detail.value
    })
  },

  onConfirmPasswordInput(e) {
    this.setData({
      'formData.confirmPassword': e.detail.value
    })
  },

  onDepartmentInput(e) {
    this.setData({
      'formData.department': e.detail.value.trim()
    })
  },

  onPositionInput(e) {
    this.setData({
      'formData.position': e.detail.value.trim()
    })
  },

  // 切换密码显示
  togglePasswordVisibility() {
    this.setData({
      showPassword: !this.data.showPassword
    })
  },

  toggleConfirmPasswordVisibility() {
    this.setData({
      showConfirmPassword: !this.data.showConfirmPassword
    })
  },

  // 表单验证
  validateForm() {
    const { name, email, password, confirmPassword } = this.data.formData

    if (!name) {
      showError('请输入姓名')
      return false
    }

    if (name.length < 2) {
      showError('姓名至少需要2个字符')
      return false
    }

    if (!email) {
      showError('请输入邮箱')
      return false
    }

    if (!validateEmail(email)) {
      showError('请输入正确的邮箱格式')
      return false
    }

    if (!password) {
      showError('请输入密码')
      return false
    }

    if (password.length < 6) {
      showError('密码长度不能少于6位')
      return false
    }

    if (!confirmPassword) {
      showError('请确认密码')
      return false
    }

    if (password !== confirmPassword) {
      showError('两次输入的密码不一致')
      return false
    }

    return true
  },

  // 注册
  async handleRegister() {
    if (!this.validateForm()) {
      return
    }

    const { formData } = this.data

    try {
      this.setData({ loading: true })

      const res = await userApi.register({
        inviteCode: formData.inviteCode,
        name: formData.name,
        email: formData.email,
        password: formData.password,
        department: formData.department,
        position: formData.position
      })

      if (res.success) {
        showSuccess('注册成功，请等待管理员审核')
        
        // 跳转到登录页面
        setTimeout(() => {
          wx.navigateBack()
        }, 2000)
      } else {
        showError(res.message || '注册失败')
      }
    } catch (error) {
      console.error('注册失败:', error)
      showError(error.message || '注册失败，请稍后再试')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 返回登录页面
  goToLogin() {
    wx.navigateBack()
  }
})
