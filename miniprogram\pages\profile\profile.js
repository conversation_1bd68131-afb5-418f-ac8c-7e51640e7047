// pages/profile/profile.js
const { userApi } = require('../../utils/api.js')
const { getUserRoleText, showError, showSuccess, showConfirm } = require('../../utils/util.js')

Page({
  data: {
    userInfo: null,
    loading: true,
    editing: false,
    tempUserInfo: {}
  },

  onLoad() {
    this.loadUserInfo()
  },

  onShow() {
    // 从其他页面返回时刷新用户信息
    if (this.data.userInfo) {
      this.loadUserInfo()
    }
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      this.setData({ loading: true })
      
      const app = getApp()
      const userId = app.globalData.userInfo?.id
      
      if (!userId) {
        showError('用户信息获取失败')
        return
      }
      
      const res = await userApi.getUserInfo(userId)
      
      if (res.success || res.id) { // 兼容不同的响应格式
        const userInfo = res.data || res
        this.setData({
          userInfo,
          tempUserInfo: { ...userInfo },
          loading: false
        })
        
        // 更新全局用户信息
        app.globalData.userInfo = userInfo
      } else {
        showError(res.message || '加载用户信息失败')
        this.setData({ loading: false })
      }
    } catch (error) {
      console.error('加载用户信息失败:', error)
      showError('加载用户信息失败')
      this.setData({ loading: false })
    }
  },

  // 开始编辑
  startEdit() {
    this.setData({
      editing: true,
      tempUserInfo: { ...this.data.userInfo }
    })
  },

  // 取消编辑
  cancelEdit() {
    this.setData({
      editing: false,
      tempUserInfo: { ...this.data.userInfo }
    })
  },

  // 保存编辑
  async saveEdit() {
    try {
      const { tempUserInfo } = this.data
      const app = getApp()
      const userId = app.globalData.userInfo?.id
      
      const res = await userApi.updateUserInfo(userId, {
        name: tempUserInfo.name,
        bio: tempUserInfo.bio,
        phone: tempUserInfo.phone,
        department: tempUserInfo.department,
        position: tempUserInfo.position
      })
      
      if (res.success || res.id) { // 兼容不同的响应格式
        const updatedUserInfo = res.data || res
        this.setData({
          userInfo: updatedUserInfo,
          tempUserInfo: { ...updatedUserInfo },
          editing: false
        })
        
        // 更新全局用户信息
        app.globalData.userInfo = updatedUserInfo
        
        showSuccess('保存成功')
      } else {
        showError(res.message || '保存失败')
      }
    } catch (error) {
      console.error('保存用户信息失败:', error)
      showError('保存失败')
    }
  },

  // 输入处理
  onNameInput(e) {
    this.setData({
      'tempUserInfo.name': e.detail.value
    })
  },

  onBioInput(e) {
    this.setData({
      'tempUserInfo.bio': e.detail.value
    })
  },

  onPhoneInput(e) {
    this.setData({
      'tempUserInfo.phone': e.detail.value
    })
  },

  onDepartmentInput(e) {
    this.setData({
      'tempUserInfo.department': e.detail.value
    })
  },

  onPositionInput(e) {
    this.setData({
      'tempUserInfo.position': e.detail.value
    })
  },

  // 选择头像
  async chooseAvatar() {
    try {
      const res = await wx.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera']
      })
      
      if (res.tempFilePaths && res.tempFilePaths.length > 0) {
        await this.uploadAvatar(res.tempFilePaths[0])
      }
    } catch (error) {
      console.error('选择头像失败:', error)
    }
  },

  // 上传头像
  async uploadAvatar(filePath) {
    try {
      wx.showLoading({
        title: '上传中...',
        mask: true
      })
      
      const res = await userApi.uploadAvatar(filePath)
      
      if (res.success) {
        const updatedUserInfo = {
          ...this.data.userInfo,
          avatar: res.data.url
        }
        
        this.setData({
          userInfo: updatedUserInfo,
          tempUserInfo: { ...updatedUserInfo }
        })
        
        // 更新全局用户信息
        const app = getApp()
        app.globalData.userInfo = updatedUserInfo
        
        showSuccess('头像更新成功')
      } else {
        showError(res.message || '上传失败')
      }
    } catch (error) {
      console.error('上传头像失败:', error)
      showError('上传失败')
    } finally {
      wx.hideLoading()
    }
  },

  // 查看统计
  viewStats() {
    wx.showModal({
      title: '个人统计',
      content: '个人统计功能正在开发中',
      showCancel: false
    })
  },

  // 设置
  openSettings() {
    wx.showActionSheet({
      itemList: ['通知设置', '隐私设置', '关于应用'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.notificationSettings()
            break
          case 1:
            this.privacySettings()
            break
          case 2:
            this.aboutApp()
            break
        }
      }
    })
  },

  // 通知设置
  notificationSettings() {
    wx.showModal({
      title: '通知设置',
      content: '通知设置功能正在开发中',
      showCancel: false
    })
  },

  // 隐私设置
  privacySettings() {
    wx.showModal({
      title: '隐私设置',
      content: '隐私设置功能正在开发中',
      showCancel: false
    })
  },

  // 关于应用
  aboutApp() {
    wx.showModal({
      title: 'LabSync 微信小程序版',
      content: '版本: 1.0.0\n\n一个现代化的实验室管理系统，专为科研团队设计。',
      showCancel: false
    })
  },

  // 退出登录
  async logout() {
    const confirmed = await showConfirm('确定要退出登录吗？')
    if (confirmed) {
      const app = getApp()
      app.logout()
    }
  },

  // 获取角色文本
  getRoleText(role) {
    return getUserRoleText(role)
  }
})
