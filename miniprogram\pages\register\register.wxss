/* pages/register/register.wxss */

.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx;
}

/* 头部 */
.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.logo {
  margin-bottom: 30rpx;
}

.logo-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 20rpx;
  background: white;
  padding: 15rpx;
}

.title {
  font-size: 44rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 12rpx;
}

.subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 50rpx;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.step-active {
  opacity: 1;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.step-active .step-number {
  background: white;
  color: #667eea;
}

.step-text {
  font-size: 22rpx;
  color: white;
}

.step-line {
  width: 80rpx;
  height: 2rpx;
  background: rgba(255, 255, 255, 0.3);
  margin: 0 20rpx;
  transition: background 0.3s ease;
}

.line-active {
  background: white;
}

/* 表单容器 */
.form-container {
  background: white;
  border-radius: 24rpx;
  padding: 50rpx 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.step-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #111827;
  text-align: center;
  margin-bottom: 12rpx;
}

.step-desc {
  font-size: 26rpx;
  color: #6B7280;
  text-align: center;
  margin-bottom: 40rpx;
}

/* 返回按钮 */
.back-btn {
  display: flex;
  align-items: center;
  color: #3B82F6;
  font-size: 26rpx;
  margin-bottom: 30rpx;
  cursor: pointer;
}

.back-icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}

/* 表单组 */
.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 12rpx;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  flex: 1;
  padding: 24rpx 60rpx 24rpx 24rpx;
  border: 2rpx solid #E5E7EB;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #F9FAFB;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #3B82F6;
  background: white;
}

.invite-code-input {
  text-transform: uppercase;
  letter-spacing: 2rpx;
  font-family: monospace;
}

.input-icon {
  position: absolute;
  right: 24rpx;
  font-size: 32rpx;
  color: #9CA3AF;
  cursor: pointer;
}

/* 可选信息区域 */
.optional-section {
  margin-top: 40rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #F3F4F6;
}

.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #6B7280;
  margin-bottom: 30rpx;
  text-align: center;
}

/* 注册按钮 */
.register-btn {
  margin-top: 20rpx;
  padding: 28rpx;
  font-size: 32rpx;
  font-weight: 600;
}

/* 表单底部 */
.form-footer {
  text-align: center;
  margin-top: 30rpx;
}

.footer-text {
  font-size: 22rpx;
  color: #9CA3AF;
  line-height: 1.5;
}

.link {
  color: #3B82F6;
  text-decoration: underline;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .register-container {
    padding: 30rpx 20rpx;
  }
  
  .form-container {
    padding: 40rpx 30rpx;
  }
  
  .step-title {
    font-size: 32rpx;
  }
  
  .step-desc {
    font-size: 24rpx;
  }
  
  .step-indicator {
    margin-bottom: 40rpx;
  }
  
  .step-line {
    width: 60rpx;
    margin: 0 15rpx;
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 600rpx) {
  .register-container {
    padding: 20rpx 15rpx;
  }
  
  .header {
    margin-bottom: 40rpx;
  }
  
  .logo-image {
    width: 80rpx;
    height: 80rpx;
  }
  
  .title {
    font-size: 36rpx;
  }
  
  .subtitle {
    font-size: 22rpx;
  }
  
  .form-container {
    padding: 30rpx 20rpx;
  }
  
  .step-indicator {
    margin-bottom: 30rpx;
  }
  
  .step-number {
    width: 50rpx;
    height: 50rpx;
    font-size: 20rpx;
  }
  
  .step-text {
    font-size: 20rpx;
  }
  
  .step-line {
    width: 40rpx;
    margin: 0 10rpx;
  }
  
  .optional-section {
    margin-top: 30rpx;
    padding-top: 20rpx;
  }
}
