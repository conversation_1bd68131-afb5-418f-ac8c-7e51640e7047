// 网络请求工具
const app = getApp()

/**
 * 发起网络请求
 * @param {Object} options 请求配置
 * @param {string} options.url 请求地址
 * @param {string} options.method 请求方法
 * @param {Object} options.data 请求数据
 * @param {boolean} options.needAuth 是否需要认证
 * @param {boolean} options.showLoading 是否显示加载中
 * @param {string} options.loadingText 加载中文本
 */
function request(options = {}) {
  const {
    url,
    method = 'GET',
    data = {},
    needAuth = true,
    showLoading = true,
    loadingText = '加载中...'
  } = options

  // 显示加载中
  if (showLoading) {
    wx.showLoading({
      title: loadingText,
      mask: true
    })
  }

  // 构建完整URL
  const fullUrl = url.startsWith('http') ? url : `${app.globalData.baseUrl}${url}`

  // 构建请求头
  const header = {
    'Content-Type': 'application/json'
  }

  // 添加认证头
  if (needAuth && app.globalData.token) {
    header['Authorization'] = `Bearer ${app.globalData.token}`
  }

  return new Promise((resolve, reject) => {
    wx.request({
      url: fullUrl,
      method: method.toUpperCase(),
      data,
      header,
      success: (res) => {
        if (showLoading) {
          wx.hideLoading()
        }

        const { statusCode, data: responseData } = res

        // 请求成功
        if (statusCode >= 200 && statusCode < 300) {
          resolve(responseData)
        } 
        // 认证失败，跳转到登录页
        else if (statusCode === 401) {
          app.logout()
          reject(new Error('登录已过期，请重新登录'))
        }
        // 其他错误
        else {
          const errorMessage = responseData?.message || `请求失败 (${statusCode})`
          wx.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 2000
          })
          reject(new Error(errorMessage))
        }
      },
      fail: (error) => {
        if (showLoading) {
          wx.hideLoading()
        }

        const errorMessage = '网络请求失败，请检查网络连接'
        wx.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 2000
        })
        reject(new Error(errorMessage))
      }
    })
  })
}

// GET请求
function get(url, data = {}, options = {}) {
  return request({
    url,
    method: 'GET',
    data,
    ...options
  })
}

// POST请求
function post(url, data = {}, options = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  })
}

// PUT请求
function put(url, data = {}, options = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

// DELETE请求
function del(url, data = {}, options = {}) {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  })
}

// 文件上传
function uploadFile(options = {}) {
  const {
    url,
    filePath,
    name = 'file',
    formData = {},
    needAuth = true,
    showLoading = true
  } = options

  if (showLoading) {
    wx.showLoading({
      title: '上传中...',
      mask: true
    })
  }

  // 构建完整URL
  const fullUrl = url.startsWith('http') ? url : `${app.globalData.baseUrl}${url}`

  // 构建请求头
  const header = {}
  if (needAuth && app.globalData.token) {
    header['Authorization'] = `Bearer ${app.globalData.token}`
  }

  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: fullUrl,
      filePath,
      name,
      formData,
      header,
      success: (res) => {
        if (showLoading) {
          wx.hideLoading()
        }

        try {
          const data = JSON.parse(res.data)
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(data)
          } else {
            const errorMessage = data?.message || '上传失败'
            wx.showToast({
              title: errorMessage,
              icon: 'none'
            })
            reject(new Error(errorMessage))
          }
        } catch (error) {
          wx.showToast({
            title: '上传失败',
            icon: 'none'
          })
          reject(error)
        }
      },
      fail: (error) => {
        if (showLoading) {
          wx.hideLoading()
        }
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        })
        reject(error)
      }
    })
  })
}

module.exports = {
  request,
  get,
  post,
  put,
  del,
  uploadFile
}
