// API接口定义
const { get, post, put, del, uploadFile } = require('./request.js')

// 用户相关API
const userApi = {
  // 登录
  login: (data) => post('/api/auth/signin', data, { needAuth: false }),
  
  // 注册
  register: (data) => post('/api/auth/signup', data, { needAuth: false }),
  
  // 获取用户信息
  getUserInfo: (id) => get(`/api/users/${id}`),
  
  // 更新用户信息
  updateUserInfo: (id, data) => put(`/api/users/${id}`, data),
  
  // 获取用户列表
  getUserList: () => get('/api/users'),
  
  // 上传头像
  uploadAvatar: (filePath) => uploadFile({
    url: '/api/upload/avatar',
    filePath,
    name: 'avatar'
  })
}

// 项目相关API
const projectApi = {
  // 获取项目列表
  getProjectList: () => get('/api/projects'),
  
  // 获取项目详情
  getProjectDetail: (id) => get(`/api/projects/${id}`),
  
  // 创建项目
  createProject: (data) => post('/api/projects', data),
  
  // 更新项目
  updateProject: (id, data) => put(`/api/projects/${id}`, data),
  
  // 删除项目
  deleteProject: (id) => del(`/api/projects/${id}`),
  
  // 添加项目成员
  addProjectMember: (id, data) => post(`/api/projects/${id}/members`, data),
  
  // 移除项目成员
  removeProjectMember: (id, userId) => del(`/api/projects/${id}/members/${userId}`),
  
  // 获取项目统计
  getProjectStats: (id) => get(`/api/projects/${id}/stats`)
}

// 任务相关API
const taskApi = {
  // 获取任务列表
  getTaskList: (projectId) => get(`/api/projects/${projectId}/tasks`),
  
  // 获取我的任务
  getMyTasks: () => get('/api/tasks/my'),
  
  // 获取任务详情
  getTaskDetail: (id) => get(`/api/tasks/${id}`),
  
  // 创建任务
  createTask: (data) => post('/api/tasks', data),
  
  // 更新任务
  updateTask: (id, data) => put(`/api/tasks/${id}`, data),
  
  // 删除任务
  deleteTask: (id) => del(`/api/tasks/${id}`),
  
  // 更新任务状态
  updateTaskStatus: (id, status) => put(`/api/tasks/${id}/status`, { status })
}

// 聊天相关API
const chatApi = {
  // 获取聊天列表
  getChatList: () => get('/api/chats'),
  
  // 获取聊天详情
  getChatDetail: (id) => get(`/api/chats/${id}`),
  
  // 创建聊天
  createChat: (data) => post('/api/chats', data),
  
  // 发送消息
  sendMessage: (chatId, data) => post(`/api/chats/${chatId}/messages`, data),
  
  // 获取消息列表
  getMessages: (chatId, page = 1) => get(`/api/chats/${chatId}/messages?page=${page}`),
  
  // 标记消息已读
  markAsRead: (chatId) => post(`/api/chats/${chatId}/read`),
  
  // 上传聊天文件
  uploadChatFile: (filePath) => uploadFile({
    url: '/api/upload/chat',
    filePath,
    name: 'file'
  })
}

// 文件相关API
const fileApi = {
  // 获取文件列表
  getFileList: (projectId) => get(`/api/projects/${projectId}/files`),
  
  // 上传文件
  uploadFile: (projectId, filePath, description = '') => uploadFile({
    url: `/api/projects/${projectId}/files`,
    filePath,
    name: 'file',
    formData: { description }
  }),
  
  // 删除文件
  deleteFile: (id) => del(`/api/files/${id}`),
  
  // 下载文件
  downloadFile: (id) => get(`/api/files/${id}/download`)
}

// 通知相关API
const notificationApi = {
  // 获取通知列表
  getNotificationList: (page = 1) => get(`/api/notifications?page=${page}`),
  
  // 标记通知已读
  markAsRead: (id) => put(`/api/notifications/${id}/read`),
  
  // 标记所有通知已读
  markAllAsRead: () => put('/api/notifications/read-all'),
  
  // 获取未读通知数量
  getUnreadCount: () => get('/api/notifications/unread-count')
}

// 仪表盘相关API
const dashboardApi = {
  // 获取仪表盘统计数据
  getStats: () => get('/api/dashboard/stats'),
  
  // 获取最近活动
  getRecentActivities: () => get('/api/dashboard/activities')
}

// 团队相关API
const teamApi = {
  // 获取团队成员
  getTeamMembers: () => get('/api/team/members'),
  
  // 邀请成员
  inviteMember: (data) => post('/api/team/invite', data),
  
  // 获取邀请码
  getInviteCodes: () => get('/api/invite-codes'),
  
  // 创建邀请码
  createInviteCode: (data) => post('/api/invite-codes', data)
}

module.exports = {
  userApi,
  projectApi,
  taskApi,
  chatApi,
  fileApi,
  notificationApi,
  dashboardApi,
  teamApi
}
