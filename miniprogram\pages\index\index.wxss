/* pages/index/index.wxss */

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 用户信息卡片 */
.user-card {
  background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
  color: white;
  margin-bottom: 30rpx;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-details {
  margin-left: 30rpx;
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.user-role {
  font-size: 26rpx;
  opacity: 0.9;
  margin-bottom: 4rpx;
}

.user-department {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stat-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.stat-card:active {
  transform: scale(0.98);
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #3B82F6;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 28rpx;
  color: #374151;
  margin-bottom: 4rpx;
}

.stat-sublabel {
  font-size: 22rpx;
  color: #9CA3AF;
}

/* 快捷操作 */
.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 20rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  transition: background 0.3s ease;
}

.action-item:active {
  background: #F3F4F6;
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.action-text {
  font-size: 24rpx;
  color: #374151;
  text-align: center;
}

/* 活动列表 */
.activity-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.activity-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #F3F4F6;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-content {
  display: flex;
  flex-direction: column;
}

.activity-title {
  font-size: 28rpx;
  color: #111827;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.activity-desc {
  font-size: 24rpx;
  color: #6B7280;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.activity-time {
  font-size: 22rpx;
  color: #9CA3AF;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 0;
}

.empty-text {
  color: #9CA3AF;
  font-size: 28rpx;
}

/* 卡片标题 */
.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #111827;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .stats-grid {
    grid-template-columns: 1fr 1fr;
    gap: 15rpx;
  }
  
  .stat-card {
    padding: 20rpx;
  }
  
  .stat-number {
    font-size: 40rpx;
  }
  
  .quick-actions {
    grid-template-columns: 1fr 1fr;
    gap: 15rpx;
  }
  
  .action-item {
    padding: 15rpx;
  }
  
  .action-icon {
    font-size: 40rpx;
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 600rpx) {
  .user-info {
    flex-direction: column;
    text-align: center;
  }
  
  .user-details {
    margin-left: 0;
    margin-top: 20rpx;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    grid-template-columns: 1fr 1fr;
  }
}
