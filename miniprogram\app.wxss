/**app.wxss**/
/* 全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 32rpx;
  line-height: 1.6;
  color: #333;
}

/* 容器样式 */
.container {
  padding: 20rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 卡片样式 */
.card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
  overflow: hidden;
}

.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-body {
  padding: 30rpx;
}

.card-footer {
  padding: 20rpx 30rpx;
  background: #fafafa;
  border-top: 1rpx solid #f0f0f0;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120rpx;
}

.btn-primary {
  background: #3B82F6;
  color: #fff;
}

.btn-primary:hover {
  background: #2563EB;
}

.btn-secondary {
  background: #6B7280;
  color: #fff;
}

.btn-success {
  background: #10B981;
  color: #fff;
}

.btn-warning {
  background: #F59E0B;
  color: #fff;
}

.btn-danger {
  background: #EF4444;
  color: #fff;
}

.btn-outline {
  background: transparent;
  border: 2rpx solid #3B82F6;
  color: #3B82F6;
}

.btn-small {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  min-width: 80rpx;
}

.btn-large {
  padding: 28rpx 56rpx;
  font-size: 32rpx;
  min-width: 200rpx;
}

.btn-block {
  width: 100%;
  display: block;
}

/* 表单样式 */
.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  margin-bottom: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
}

.form-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #E5E7EB;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #3B82F6;
  outline: none;
}

.form-textarea {
  min-height: 120rpx;
  resize: vertical;
}

/* 列表样式 */
.list {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.list-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  transition: background 0.3s ease;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:hover {
  background: #f9f9f9;
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #111827;
  margin-bottom: 8rpx;
}

.list-item-desc {
  font-size: 26rpx;
  color: #6B7280;
}

.list-item-action {
  margin-left: 20rpx;
}

/* 头像样式 */
.avatar {
  border-radius: 50%;
  overflow: hidden;
  display: inline-block;
}

.avatar-small {
  width: 60rpx;
  height: 60rpx;
}

.avatar-medium {
  width: 80rpx;
  height: 80rpx;
}

.avatar-large {
  width: 120rpx;
  height: 120rpx;
}

/* 标签样式 */
.tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  font-weight: 500;
  line-height: 1;
}

.tag-primary {
  background: #EBF4FF;
  color: #3B82F6;
}

.tag-success {
  background: #ECFDF5;
  color: #10B981;
}

.tag-warning {
  background: #FFFBEB;
  color: #F59E0B;
}

.tag-danger {
  background: #FEF2F2;
  color: #EF4444;
}

.tag-gray {
  background: #F3F4F6;
  color: #6B7280;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.mb-10 {
  margin-bottom: 10rpx;
}

.mb-20 {
  margin-bottom: 20rpx;
}

.mb-30 {
  margin-bottom: 30rpx;
}

.mt-10 {
  margin-top: 10rpx;
}

.mt-20 {
  margin-top: 20rpx;
}

.mt-30 {
  margin-top: 30rpx;
}

.p-20 {
  padding: 20rpx;
}

.p-30 {
  padding: 30rpx;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .container {
    padding: 15rpx;
  }
  
  .card-body {
    padding: 20rpx;
  }
  
  .btn {
    padding: 16rpx 32rpx;
    font-size: 26rpx;
  }
}
