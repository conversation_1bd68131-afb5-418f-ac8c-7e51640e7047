/* pages/login/login.wxss */

.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* 头部 */
.header {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo {
  margin-bottom: 30rpx;
}

.logo-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  background: white;
  padding: 20rpx;
}

.title {
  font-size: 48rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 12rpx;
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 表单容器 */
.form-container {
  background: white;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 40rpx;
}

.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 16rpx;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  flex: 1;
  padding: 24rpx 60rpx 24rpx 24rpx;
  border: 2rpx solid #E5E7EB;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #F9FAFB;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #3B82F6;
  background: white;
}

.input-icon {
  position: absolute;
  right: 24rpx;
  font-size: 32rpx;
  color: #9CA3AF;
  cursor: pointer;
}

.login-btn {
  margin-top: 20rpx;
  padding: 28rpx;
  font-size: 32rpx;
  font-weight: 600;
}

/* 表单操作 */
.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
}

.action-link {
  font-size: 26rpx;
  color: #3B82F6;
  cursor: pointer;
}

.action-link:active {
  opacity: 0.7;
}

/* 分割线 */
.divider {
  display: flex;
  align-items: center;
  margin: 40rpx 0;
}

.divider-line {
  flex: 1;
  height: 1rpx;
  background: rgba(255, 255, 255, 0.3);
}

.divider-text {
  margin: 0 20rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 微信登录 */
.wechat-login {
  margin-bottom: 40rpx;
}

.wechat-btn {
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  font-size: 28rpx;
}

.wechat-icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

/* 底部 */
.footer {
  text-align: center;
  margin-top: auto;
}

.footer-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
}

.link {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: underline;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .login-container {
    padding: 40rpx 30rpx;
  }
  
  .form-container {
    padding: 40rpx 30rpx;
  }
  
  .title {
    font-size: 40rpx;
  }
  
  .subtitle {
    font-size: 24rpx;
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 600rpx) {
  .login-container {
    padding: 30rpx 20rpx;
  }
  
  .header {
    margin-bottom: 60rpx;
  }
  
  .logo-image {
    width: 100rpx;
    height: 100rpx;
  }
  
  .form-container {
    padding: 30rpx 20rpx;
  }
  
  .form-actions {
    flex-direction: column;
    align-items: center;
    gap: 20rpx;
  }
}
