// pages/chat/chat.js
const { chatApi } = require('../../utils/api.js')
const { formatRelativeTime, showError } = require('../../utils/util.js')

Page({
  data: {
    chats: [],
    loading: true,
    refreshing: false,
    searchText: '',
    unreadCount: 0
  },

  onLoad() {
    this.loadChats()
  },

  onShow() {
    // 每次显示页面时刷新聊天列表
    this.loadChats()
  },

  // 加载聊天列表
  async loadChats() {
    try {
      this.setData({ loading: true })
      
      const res = await chatApi.getChatList()
      
      if (res.success) {
        const chats = res.data || []
        const unreadCount = chats.reduce((count, chat) => count + (chat.unreadCount || 0), 0)
        
        this.setData({
          chats,
          unreadCount,
          loading: false
        })
        
        // 更新tabBar徽章
        if (unreadCount > 0) {
          wx.setTabBarBadge({
            index: 3, // 聊天tab的索引
            text: unreadCount > 99 ? '99+' : unreadCount.toString()
          })
        } else {
          wx.removeTabBarBadge({
            index: 3
          })
        }
      } else {
        showError(res.message || '加载聊天列表失败')
        this.setData({ loading: false })
      }
    } catch (error) {
      console.error('加载聊天列表失败:', error)
      showError('加载聊天列表失败')
      this.setData({ loading: false })
    }
  },

  // 下拉刷新
  async onPullDownRefresh() {
    this.setData({ refreshing: true })
    await this.loadChats()
    this.setData({ refreshing: false })
    wx.stopPullDownRefresh()
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchText: e.detail.value
    })
  },

  // 获取过滤后的聊天列表
  getFilteredChats() {
    const { chats, searchText } = this.data
    
    if (!searchText) return chats
    
    return chats.filter(chat => {
      const chatName = this.getChatName(chat)
      return chatName.toLowerCase().includes(searchText.toLowerCase())
    })
  },

  // 获取聊天名称
  getChatName(chat) {
    if (chat.type === 'GROUP') {
      return chat.name || '群聊'
    } else {
      // 私聊：显示对方的名字
      const app = getApp()
      const currentUserId = app.globalData.userInfo?.id
      const otherUser = chat.participants?.find(p => p.id !== currentUserId)
      return otherUser?.name || '未知用户'
    }
  },

  // 获取聊天头像
  getChatAvatar(chat) {
    if (chat.type === 'GROUP') {
      return '/images/group-chat.png'
    } else {
      // 私聊：显示对方的头像
      const app = getApp()
      const currentUserId = app.globalData.userInfo?.id
      const otherUser = chat.participants?.find(p => p.id !== currentUserId)
      return otherUser?.avatar || '/images/default-avatar.png'
    }
  },

  // 获取最后一条消息的显示文本
  getLastMessageText(chat) {
    const lastMessage = chat.lastMessage
    if (!lastMessage) return '暂无消息'
    
    switch (lastMessage.type) {
      case 'TEXT':
        return lastMessage.content
      case 'IMAGE':
        return '[图片]'
      case 'FILE':
        return `[文件] ${lastMessage.fileName || '文件'}`
      case 'SYSTEM':
        return lastMessage.content
      default:
        return '消息'
    }
  },

  // 跳转到聊天详情
  goToChatDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/chat-detail/chat-detail?id=${id}`
    })
  },

  // 创建新聊天
  createChat() {
    wx.showActionSheet({
      itemList: ['发起私聊', '创建群聊'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.createPrivateChat()
        } else if (res.tapIndex === 1) {
          this.createGroupChat()
        }
      }
    })
  },

  // 创建私聊
  createPrivateChat() {
    wx.showModal({
      title: '发起私聊',
      content: '私聊创建功能正在开发中',
      showCancel: false
    })
  },

  // 创建群聊
  createGroupChat() {
    wx.showModal({
      title: '创建群聊',
      content: '群聊创建功能正在开发中',
      showCancel: false
    })
  },

  // 格式化时间
  formatTime(time) {
    return formatRelativeTime(time)
  }
})
