<!--pages/chat/chat.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-box">
      <input
        class="search-input"
        type="text"
        placeholder="搜索聊天..."
        value="{{searchText}}"
        bindinput="onSearchInput"
      />
      <view class="search-icon">🔍</view>
    </view>
  </view>

  <!-- 加载中 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 聊天列表 -->
  <view wx:else>
    <!-- 空状态 -->
    <view wx:if="{{getFilteredChats().length === 0}}" class="empty-state">
      <view class="empty-icon">💬</view>
      <view class="empty-text">
        {{searchText ? '没有找到匹配的聊天' : '暂无聊天记录'}}
      </view>
      <button wx:if="{{!searchText}}" 
              class="btn btn-primary" 
              bindtap="createChat">
        开始聊天
      </button>
    </view>

    <!-- 聊天卡片列表 -->
    <view wx:else class="chat-list">
      <view
        wx:for="{{getFilteredChats()}}"
        wx:key="id"
        class="chat-item"
        bindtap="goToChatDetail"
        data-id="{{item.id}}"
      >
        <!-- 头像 -->
        <view class="chat-avatar-container">
          <image 
            class="chat-avatar" 
            src="{{getChatAvatar(item)}}"
            mode="aspectFill"
          />
          <view wx:if="{{item.unreadCount > 0}}" class="unread-badge">
            {{item.unreadCount > 99 ? '99+' : item.unreadCount}}
          </view>
        </view>

        <!-- 聊天信息 -->
        <view class="chat-info">
          <view class="chat-header">
            <view class="chat-name">{{getChatName(item)}}</view>
            <view class="chat-time">{{formatTime(item.lastMessage.createdAt)}}</view>
          </view>
          
          <view class="chat-preview">
            <view class="last-message">{{getLastMessageText(item)}}</view>
            <view wx:if="{{item.type === 'GROUP'}}" class="member-count">
              {{item.participants.length}}人
            </view>
          </view>
        </view>

        <!-- 在线状态（仅私聊显示） -->
        <view wx:if="{{item.type === 'PRIVATE' && item.otherUserOnline}}" class="online-indicator"></view>
      </view>
    </view>
  </view>

  <!-- 浮动创建按钮 -->
  <view class="fab" bindtap="createChat">
    <view class="fab-icon">💬</view>
  </view>
</view>
