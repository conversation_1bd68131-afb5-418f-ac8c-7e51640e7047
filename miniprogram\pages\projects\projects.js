// pages/projects/projects.js
const { projectApi } = require('../../utils/api.js')
const { formatTime, getProjectStatusText, showError } = require('../../utils/util.js')

Page({
  data: {
    projects: [],
    loading: true,
    refreshing: false,
    searchText: '',
    filterStatus: 'ALL', // ALL, PLANNING, ACTIVE, COMPLETED, ARCHIVED
    statusOptions: [
      { value: 'ALL', label: '全部' },
      { value: 'PLANNING', label: '计划中' },
      { value: 'ACTIVE', label: '进行中' },
      { value: 'COMPLETED', label: '已完成' },
      { value: 'ARCHIVED', label: '已归档' }
    ]
  },

  onLoad() {
    this.loadProjects()
  },

  onShow() {
    // 从项目详情页返回时刷新列表
    if (this.data.projects.length > 0) {
      this.loadProjects()
    }
  },

  // 加载项目列表
  async loadProjects() {
    try {
      this.setData({ loading: true })
      
      const res = await projectApi.getProjectList()
      
      if (res.success) {
        this.setData({
          projects: res.data || [],
          loading: false
        })
      } else {
        showError(res.message || '加载项目列表失败')
        this.setData({ loading: false })
      }
    } catch (error) {
      console.error('加载项目列表失败:', error)
      showError('加载项目列表失败')
      this.setData({ loading: false })
    }
  },

  // 下拉刷新
  async onPullDownRefresh() {
    this.setData({ refreshing: true })
    await this.loadProjects()
    this.setData({ refreshing: false })
    wx.stopPullDownRefresh()
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchText: e.detail.value
    })
  },

  // 状态筛选
  onStatusChange(e) {
    this.setData({
      filterStatus: e.detail.value
    })
  },

  // 获取过滤后的项目列表
  getFilteredProjects() {
    const { projects, searchText, filterStatus } = this.data
    
    return projects.filter(project => {
      // 搜索过滤
      const matchSearch = !searchText || 
        project.title.toLowerCase().includes(searchText.toLowerCase()) ||
        (project.description && project.description.toLowerCase().includes(searchText.toLowerCase()))
      
      // 状态过滤
      const matchStatus = filterStatus === 'ALL' || project.status === filterStatus
      
      return matchSearch && matchStatus
    })
  },

  // 跳转到项目详情
  goToProjectDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/project-detail/project-detail?id=${id}`
    })
  },

  // 创建新项目
  createProject() {
    wx.showModal({
      title: '创建项目',
      content: '项目创建功能正在开发中',
      showCancel: false
    })
  },

  // 获取项目状态文本
  getStatusText(status) {
    return getProjectStatusText(status)
  },

  // 获取项目状态样式类
  getStatusClass(status) {
    const classMap = {
      'PLANNING': 'tag-gray',
      'ACTIVE': 'tag-primary',
      'COMPLETED': 'tag-success',
      'ARCHIVED': 'tag-warning'
    }
    return classMap[status] || 'tag-gray'
  },

  // 格式化时间
  formatTime(time) {
    return formatTime(time, 'YYYY-MM-DD')
  },

  // 计算项目进度颜色
  getProgressColor(progress) {
    if (progress < 30) return '#EF4444'
    if (progress < 70) return '#F59E0B'
    return '#10B981'
  }
})
