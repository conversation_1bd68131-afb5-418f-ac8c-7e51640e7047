// pages/index/index.js
const { dashboardApi, notificationApi } = require('../../utils/api.js')
const { formatTime, showError } = require('../../utils/util.js')

Page({
  data: {
    userInfo: null,
    stats: {
      totalProjects: 0,
      activeProjects: 0,
      completedTasks: 0,
      pendingTasks: 0,
      totalUsers: 0,
      unreadNotifications: 0
    },
    recentActivities: [],
    loading: true,
    refreshing: false
  },

  onLoad() {
    this.checkLogin()
  },

  onShow() {
    if (this.data.userInfo) {
      this.loadDashboardData()
      this.loadUnreadCount()
    }
  },

  // 检查登录状态
  checkLogin() {
    const app = getApp()
    if (!app.isLoggedIn()) {
      wx.reLaunch({
        url: '/pages/login/login'
      })
      return
    }
    
    this.setData({
      userInfo: app.globalData.userInfo
    })
    
    this.loadDashboardData()
    this.loadUnreadCount()
  },

  // 加载仪表盘数据
  async loadDashboardData() {
    try {
      this.setData({ loading: true })
      
      const [statsRes, activitiesRes] = await Promise.all([
        dashboardApi.getStats(),
        dashboardApi.getRecentActivities()
      ])
      
      this.setData({
        stats: statsRes.data?.stats || this.data.stats,
        recentActivities: activitiesRes.data || [],
        loading: false
      })
    } catch (error) {
      console.error('加载仪表盘数据失败:', error)
      showError('加载数据失败')
      this.setData({ loading: false })
    }
  },

  // 加载未读通知数量
  async loadUnreadCount() {
    try {
      const res = await notificationApi.getUnreadCount()
      this.setData({
        'stats.unreadNotifications': res.data?.count || 0
      })
    } catch (error) {
      console.error('加载未读通知数量失败:', error)
    }
  },

  // 下拉刷新
  async onPullDownRefresh() {
    this.setData({ refreshing: true })
    await this.loadDashboardData()
    await this.loadUnreadCount()
    this.setData({ refreshing: false })
    wx.stopPullDownRefresh()
  },

  // 跳转到项目页面
  goToProjects() {
    wx.switchTab({
      url: '/pages/projects/projects'
    })
  },

  // 跳转到任务页面
  goToTasks() {
    wx.switchTab({
      url: '/pages/tasks/tasks'
    })
  },

  // 跳转到聊天页面
  goToChat() {
    wx.switchTab({
      url: '/pages/chat/chat'
    })
  },

  // 跳转到通知页面
  goToNotifications() {
    wx.navigateTo({
      url: '/pages/notifications/notifications'
    })
  },

  // 跳转到团队页面
  goToTeam() {
    wx.navigateTo({
      url: '/pages/team/team'
    })
  },

  // 跳转到个人资料页面
  goToProfile() {
    wx.switchTab({
      url: '/pages/profile/profile'
    })
  },

  // 格式化时间
  formatTime(time) {
    return formatTime(time, 'MM-DD HH:mm')
  }
})
