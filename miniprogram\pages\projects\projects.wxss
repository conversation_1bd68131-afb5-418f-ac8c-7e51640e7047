/* pages/projects/projects.wxss */

/* 搜索和筛选 */
.search-filter {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
  padding: 0 20rpx;
}

.search-box {
  flex: 1;
  position: relative;
}

.search-input {
  width: 100%;
  padding: 24rpx 60rpx 24rpx 24rpx;
  border: 2rpx solid #E5E7EB;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: white;
  box-sizing: border-box;
}

.search-input:focus {
  border-color: #3B82F6;
}

.search-icon {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 28rpx;
  color: #9CA3AF;
}

.filter-picker {
  min-width: 120rpx;
}

.filter-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 20rpx;
  border: 2rpx solid #E5E7EB;
  border-radius: 12rpx;
  background: white;
  font-size: 26rpx;
  color: #374151;
}

.filter-arrow {
  margin-left: 8rpx;
  font-size: 20rpx;
  color: #9CA3AF;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #9CA3AF;
  font-size: 28rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #9CA3AF;
  margin-bottom: 40rpx;
}

/* 项目列表 */
.project-list {
  padding: 0 20rpx;
}

.project-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.project-card:active {
  transform: scale(0.98);
}

/* 项目头部 */
.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.project-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  color: #111827;
  margin-right: 20rpx;
  line-height: 1.4;
}

/* 项目描述 */
.project-desc {
  font-size: 26rpx;
  color: #6B7280;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 项目进度 */
.project-progress {
  margin-bottom: 20rpx;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
  font-size: 24rpx;
  color: #6B7280;
}

.progress-percent {
  font-weight: 600;
  color: #374151;
}

.progress-bar {
  height: 8rpx;
  background: #F3F4F6;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 项目信息 */
.project-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 22rpx;
  color: #6B7280;
}

.info-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}

.info-text {
  white-space: nowrap;
}

/* 项目负责人 */
.project-owner {
  display: flex;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #F3F4F6;
}

.owner-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.owner-name {
  font-size: 24rpx;
  color: #6B7280;
}

/* 浮动按钮 */
.fab {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  width: 112rpx;
  height: 112rpx;
  background: #3B82F6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.3);
  z-index: 100;
  transition: transform 0.3s ease;
}

.fab:active {
  transform: scale(0.9);
}

.fab-icon {
  font-size: 48rpx;
  color: white;
  font-weight: 300;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .search-filter {
    padding: 0 15rpx;
    gap: 15rpx;
  }
  
  .project-list {
    padding: 0 15rpx;
  }
  
  .project-card {
    padding: 20rpx;
  }
  
  .project-title {
    font-size: 28rpx;
  }
  
  .project-info {
    flex-wrap: wrap;
    gap: 10rpx;
  }
  
  .info-item {
    min-width: 80rpx;
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 600rpx) {
  .search-filter {
    flex-direction: column;
    gap: 15rpx;
  }
  
  .filter-picker {
    min-width: auto;
  }
  
  .project-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15rpx;
  }
  
  .project-info {
    flex-direction: column;
    gap: 12rpx;
  }
  
  .fab {
    bottom: 100rpx;
    right: 30rpx;
    width: 96rpx;
    height: 96rpx;
  }
  
  .fab-icon {
    font-size: 40rpx;
  }
}
