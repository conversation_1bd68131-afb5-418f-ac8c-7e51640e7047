// components/loading/loading.js
Component({
  properties: {
    // 是否显示加载中
    show: {
      type: Boolean,
      value: false
    },
    // 加载文本
    text: {
      type: String,
      value: '加载中...'
    },
    // 加载类型：spinner, dots, pulse
    type: {
      type: String,
      value: 'spinner'
    },
    // 大小：small, medium, large
    size: {
      type: String,
      value: 'medium'
    },
    // 是否显示遮罩
    mask: {
      type: Boolean,
      value: false
    }
  },

  data: {
    
  },

  methods: {
    // 点击遮罩
    onMaskTap() {
      // 如果有遮罩，点击时不做任何操作（防止穿透）
    }
  }
})
