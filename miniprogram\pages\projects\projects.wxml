<!--pages/projects/projects.wxml-->
<view class="container">
  <!-- 搜索和筛选 -->
  <view class="search-filter">
    <view class="search-box">
      <input
        class="search-input"
        type="text"
        placeholder="搜索项目..."
        value="{{searchText}}"
        bindinput="onSearchInput"
      />
      <view class="search-icon">🔍</view>
    </view>
    
    <picker
      class="filter-picker"
      mode="selector"
      range="{{statusOptions}}"
      range-key="label"
      value="{{filterStatus}}"
      bindchange="onStatusChange"
    >
      <view class="filter-btn">
        <text>{{statusOptions[filterStatus] || '全部'}}</text>
        <text class="filter-arrow">▼</text>
      </view>
    </picker>
  </view>

  <!-- 加载中 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 项目列表 -->
  <view wx:else>
    <!-- 空状态 -->
    <view wx:if="{{getFilteredProjects().length === 0}}" class="empty-state">
      <view class="empty-icon">📋</view>
      <view class="empty-text">
        {{searchText || filterStatus !== 'ALL' ? '没有找到匹配的项目' : '暂无项目'}}
      </view>
      <button wx:if="{{!searchText && filterStatus === 'ALL'}}" 
              class="btn btn-primary" 
              bindtap="createProject">
        创建第一个项目
      </button>
    </view>

    <!-- 项目卡片列表 -->
    <view wx:else class="project-list">
      <view
        wx:for="{{getFilteredProjects()}}"
        wx:key="id"
        class="project-card"
        bindtap="goToProjectDetail"
        data-id="{{item.id}}"
      >
        <!-- 项目头部 -->
        <view class="project-header">
          <view class="project-title">{{item.title}}</view>
          <view class="tag {{getStatusClass(item.status)}}">
            {{getStatusText(item.status)}}
          </view>
        </view>

        <!-- 项目描述 -->
        <view wx:if="{{item.description}}" class="project-desc">
          {{item.description}}
        </view>

        <!-- 项目进度 -->
        <view class="project-progress">
          <view class="progress-label">
            <text>进度</text>
            <text class="progress-percent">{{item.progress}}%</text>
          </view>
          <view class="progress-bar">
            <view 
              class="progress-fill" 
              style="width: {{item.progress}}%; background-color: {{getProgressColor(item.progress)}}"
            ></view>
          </view>
        </view>

        <!-- 项目信息 -->
        <view class="project-info">
          <view class="info-item">
            <view class="info-icon">👥</view>
            <view class="info-text">{{item.members ? item.members.length : 0}} 成员</view>
          </view>
          <view class="info-item">
            <view class="info-icon">✅</view>
            <view class="info-text">{{item.tasks ? item.tasks.length : 0}} 任务</view>
          </view>
          <view class="info-item">
            <view class="info-icon">📅</view>
            <view class="info-text">{{formatTime(item.startDate)}}</view>
          </view>
        </view>

        <!-- 项目负责人 -->
        <view class="project-owner">
          <image 
            class="owner-avatar" 
            src="{{item.owner.avatar || '/images/default-avatar.png'}}"
            mode="aspectFill"
          />
          <view class="owner-name">{{item.owner.name}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 浮动创建按钮 -->
  <view class="fab" bindtap="createProject">
    <view class="fab-icon">+</view>
  </view>
</view>
